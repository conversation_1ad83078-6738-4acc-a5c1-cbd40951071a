#!/usr/bin/env node

/**
 * Comprehensive Rate Limiting Test Script
 * Tests API rate limiting and WebSocket connection limits for all tiers
 */

import axios from 'axios';
import { WebSocket } from 'ws';
import { performance } from 'perf_hooks';

// Configuration
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
const WS_BASE_URL = process.env.WS_BASE_URL || 'ws://localhost:3001';

// Test user API keys for each tier
const TEST_USERS = {
    free: {
        apiKey: 'test_free_api_key_12345',
        tier: 'Free',
        expectedRateLimit: 10, // requests per minute
        expectedWSConnections: 1
    },
    basic: {
        apiKey: 'test_basic_api_key_12345',
        tier: 'Basic',
        expectedRateLimit: 60,
        expectedWSConnections: 3
    },
    premium: {
        apiKey: 'test_premium_api_key_12345',
        tier: 'Premium',
        expectedRateLimit: 300,
        expectedWSConnections: 5
    },
    enterprise: {
        apiKey: 'test_enterprise_api_key_12345',
        tier: 'Enterprise',
        expectedRateLimit: 1000,
        expectedWSConnections: 10
    },
    admin: {
        apiKey: 'admin_api_key_super_secure_change_in_production',
        tier: 'Admin',
        expectedRateLimit: -1, // unlimited
        expectedWSConnections: -1 // unlimited
    }
};

// Test results storage
const testResults = {
    apiRateLimiting: {},
    websocketLimiting: {},
    summary: {
        passed: 0,
        failed: 0,
        total: 0
    }
};

// Utility functions
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function logTest(message, status = 'info') {
    const timestamp = new Date().toISOString();
    const symbols = { info: 'ℹ️', success: '✅', error: '❌', warning: '⚠️' };
    console.log(`${symbols[status]} [${timestamp}] ${message}`);
}

function updateSummary(passed) {
    testResults.summary.total++;
    if (passed) {
        testResults.summary.passed++;
    } else {
        testResults.summary.failed++;
    }
}

// API Rate Limiting Tests
async function testAPIRateLimit(userType, userConfig) {
    logTest(`Testing API rate limiting for ${userConfig.tier} tier...`);

    const { apiKey, expectedRateLimit } = userConfig;
    // Use test endpoint for authenticated rate limiting, status for public
    const testEndpoint = userType === 'admin' ? '/api/v1/test/rate-limit' : '/api/v1/test/rate-limit';
    const testUrl = `${BASE_URL}${testEndpoint}`;
    
    try {
        // Test 1: Normal requests within limit
        logTest(`  Testing normal requests for ${userConfig.tier}...`);
        
        const normalRequests = Math.min(expectedRateLimit === -1 ? 5 : Math.floor(expectedRateLimit * 0.8), 50);
        let successCount = 0;
        let rateLimitHit = false;
        
        const startTime = performance.now();
        
        for (let i = 0; i < normalRequests; i++) {
            try {
                const response = await axios.get(testUrl, {
                    headers: { 'X-API-Key': apiKey },
                    timeout: 5000
                });
                
                if (response.status === 200) {
                    successCount++;
                } else if (response.status === 429) {
                    rateLimitHit = true;
                    logTest(`    Rate limit hit at request ${i + 1}`, 'warning');
                    break;
                }
            } catch (error) {
                if (error.response?.status === 429) {
                    rateLimitHit = true;
                    logTest(`    Rate limit hit at request ${i + 1}`, 'warning');
                    break;
                } else {
                    logTest(`    Request ${i + 1} failed: ${error.message}`, 'error');
                }
            }
            
            // Small delay to avoid overwhelming the server
            await sleep(100);
        }
        
        const endTime = performance.now();
        const duration = (endTime - startTime) / 1000;
        
        logTest(`    Completed ${successCount}/${normalRequests} requests in ${duration.toFixed(2)}s`);
        
        // Test 2: Burst requests to trigger rate limit (except for unlimited tiers)
        if (expectedRateLimit !== -1) {
            logTest(`  Testing rate limit enforcement for ${userConfig.tier}...`);
            
            const burstRequests = expectedRateLimit + 10;
            let burstSuccessCount = 0;
            let firstRateLimitAt = null;
            
            for (let i = 0; i < burstRequests; i++) {
                try {
                    const response = await axios.get(testUrl, {
                        headers: { 'X-API-Key': apiKey },
                        timeout: 2000
                    });
                    
                    if (response.status === 200) {
                        burstSuccessCount++;
                    }
                } catch (error) {
                    if (error.response?.status === 429 && !firstRateLimitAt) {
                        firstRateLimitAt = i + 1;
                        logTest(`    Rate limit triggered at request ${firstRateLimitAt}`, 'success');
                        break;
                    }
                }
            }
            
            const rateLimitWorking = firstRateLimitAt !== null && firstRateLimitAt <= expectedRateLimit + 5;
            
            testResults.apiRateLimiting[userType] = {
                tier: userConfig.tier,
                expectedLimit: expectedRateLimit,
                normalRequestsSuccess: successCount,
                rateLimitTriggeredAt: firstRateLimitAt,
                rateLimitWorking: rateLimitWorking,
                passed: rateLimitWorking
            };
            
            if (rateLimitWorking) {
                logTest(`  ✅ Rate limiting working correctly for ${userConfig.tier}`, 'success');
                updateSummary(true);
            } else {
                logTest(`  ❌ Rate limiting NOT working for ${userConfig.tier}`, 'error');
                updateSummary(false);
            }
        } else {
            // For unlimited tiers, just verify they can make many requests
            testResults.apiRateLimiting[userType] = {
                tier: userConfig.tier,
                expectedLimit: 'unlimited',
                normalRequestsSuccess: successCount,
                rateLimitTriggeredAt: null,
                rateLimitWorking: true,
                passed: true
            };
            
            logTest(`  ✅ Unlimited access working for ${userConfig.tier}`, 'success');
            updateSummary(true);
        }
        
    } catch (error) {
        logTest(`  ❌ API rate limit test failed for ${userConfig.tier}: ${error.message}`, 'error');
        testResults.apiRateLimiting[userType] = {
            tier: userConfig.tier,
            error: error.message,
            passed: false
        };
        updateSummary(false);
    }
}

// WebSocket Connection Limit Tests
async function testWebSocketLimits(userType, userConfig) {
    logTest(`Testing WebSocket connection limits for ${userConfig.tier} tier...`);
    
    const { apiKey, expectedWSConnections } = userConfig;
    const connections = [];
    let successfulConnections = 0;
    let firstRejectionAt = null;
    
    try {
        const maxTestConnections = expectedWSConnections === -1 ? 15 : expectedWSConnections + 5;
        
        for (let i = 0; i < maxTestConnections; i++) {
            try {
                const ws = new WebSocket(`${WS_BASE_URL}/ws?apiKey=${apiKey}`);
                
                const connectionResult = await new Promise((resolve) => {
                    let hasResolved = false;

                    const timeout = setTimeout(() => {
                        if (!hasResolved) {
                            hasResolved = true;
                            resolve({ success: false, reason: 'timeout' });
                        }
                    }, 5000);

                    ws.on('open', () => {
                        // Don't resolve immediately - wait for authentication result
                    });

                    ws.on('message', (data) => {
                        if (!hasResolved) {
                            try {
                                const message = JSON.parse(data.toString());
                                if (message.type === 'connected') {
                                    hasResolved = true;
                                    clearTimeout(timeout);
                                    resolve({ success: true, ws });
                                }
                            } catch (error) {
                                // Ignore non-JSON messages
                            }
                        }
                    });

                    ws.on('close', (code, reason) => {
                        if (!hasResolved) {
                            hasResolved = true;
                            clearTimeout(timeout);
                            if (code === 1013) {
                                resolve({ success: false, reason: 'connection_limit_exceeded' });
                            } else if (code === 1008) {
                                resolve({ success: false, reason: 'authentication_failed' });
                            } else {
                                resolve({ success: false, reason: `closed_${code}` });
                            }
                        }
                    });

                    ws.on('error', (error) => {
                        if (!hasResolved) {
                            hasResolved = true;
                            clearTimeout(timeout);
                            resolve({ success: false, reason: error.message });
                        }
                    });
                });
                
                if (connectionResult.success) {
                    connections.push(connectionResult.ws);
                    successfulConnections++;
                    logTest(`    Connection ${i + 1}: Success`);
                } else {
                    if (!firstRejectionAt) {
                        firstRejectionAt = i + 1;
                    }
                    logTest(`    Connection ${i + 1}: Rejected (${connectionResult.reason})`);

                    // Only break if it's a connection limit rejection or authentication failure
                    // This ensures we properly detect when limits are working
                    if (connectionResult.reason === 'connection_limit_exceeded' ||
                        connectionResult.reason === 'authentication_failed') {
                        break;
                    }
                }
                
                await sleep(500); // Small delay between connections
                
            } catch (error) {
                if (!firstRejectionAt) {
                    firstRejectionAt = i + 1;
                }
                logTest(`    Connection ${i + 1}: Error - ${error.message}`);
                break;
            }
        }
        
        // Clean up connections
        connections.forEach(ws => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });
        
        // For admin users (-1 limit), they should have unlimited access
        // For other users, the first rejection should happen at or near the expected limit
        const limitWorking = expectedWSConnections === -1 ?
                           (successfulConnections >= 10) : // Admin should be able to connect many times
                           (firstRejectionAt !== null && firstRejectionAt <= expectedWSConnections + 1);
        
        testResults.websocketLimiting[userType] = {
            tier: userConfig.tier,
            expectedLimit: expectedWSConnections,
            successfulConnections: successfulConnections,
            firstRejectionAt: firstRejectionAt,
            limitWorking: limitWorking,
            passed: limitWorking
        };
        
        if (limitWorking) {
            logTest(`  ✅ WebSocket limits working correctly for ${userConfig.tier}`, 'success');
            updateSummary(true);
        } else {
            logTest(`  ❌ WebSocket limits NOT working for ${userConfig.tier}`, 'error');
            updateSummary(false);
        }
        
    } catch (error) {
        logTest(`  ❌ WebSocket limit test failed for ${userConfig.tier}: ${error.message}`, 'error');
        testResults.websocketLimiting[userType] = {
            tier: userConfig.tier,
            error: error.message,
            passed: false
        };
        updateSummary(false);
    }
}

// Main test runner
async function runRateLimitingTests() {
    logTest('🚀 Starting comprehensive rate limiting tests...', 'info');
    logTest(`Testing against: ${BASE_URL}`, 'info');
    
    // Test API rate limiting for each tier
    logTest('\n📊 Testing API Rate Limiting...', 'info');
    for (const [userType, userConfig] of Object.entries(TEST_USERS)) {
        await testAPIRateLimit(userType, userConfig);
        await sleep(2000); // Wait between tier tests
    }
    
    // Test WebSocket connection limits for each tier
    logTest('\n🔌 Testing WebSocket Connection Limits...', 'info');
    for (const [userType, userConfig] of Object.entries(TEST_USERS)) {
        await testWebSocketLimits(userType, userConfig);
        await sleep(2000); // Wait between tier tests
    }
    
    // Print summary
    printTestSummary();
}

function printTestSummary() {
    console.log('\n' + '='.repeat(80));
    logTest('📋 RATE LIMITING TEST SUMMARY', 'info');
    console.log('='.repeat(80));
    
    console.log('\n📊 API Rate Limiting Results:');
    Object.entries(testResults.apiRateLimiting).forEach(([userType, result]) => {
        const status = result.passed ? '✅' : '❌';
        console.log(`  ${status} ${result.tier}: Expected ${result.expectedLimit}/min, ` +
                   `Limit triggered at request ${result.rateLimitTriggeredAt || 'N/A'}`);
    });
    
    console.log('\n🔌 WebSocket Connection Limiting Results:');
    Object.entries(testResults.websocketLimiting).forEach(([userType, result]) => {
        const status = result.passed ? '✅' : '❌';
        console.log(`  ${status} ${result.tier}: Expected ${result.expectedLimit} connections, ` +
                   `Got ${result.successfulConnections}, Rejected at ${result.firstRejectionAt || 'N/A'}`);
    });
    
    console.log('\n📈 Overall Results:');
    console.log(`  Total Tests: ${testResults.summary.total}`);
    console.log(`  Passed: ${testResults.summary.passed}`);
    console.log(`  Failed: ${testResults.summary.failed}`);
    console.log(`  Success Rate: ${((testResults.summary.passed / testResults.summary.total) * 100).toFixed(1)}%`);
    
    if (testResults.summary.failed === 0) {
        logTest('🎉 All rate limiting tests passed!', 'success');
    } else {
        logTest(`⚠️ ${testResults.summary.failed} test(s) failed. Check the results above.`, 'warning');
    }
    
    console.log('\n' + '='.repeat(80));
}

// Run the tests
if (import.meta.url === `file://${process.argv[1]}`) {
    runRateLimitingTests().catch(error => {
        logTest(`Fatal error: ${error.message}`, 'error');
        process.exit(1);
    });
}

export { runRateLimitingTests, TEST_USERS };
