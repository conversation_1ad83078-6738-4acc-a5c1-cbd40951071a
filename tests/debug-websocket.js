#!/usr/bin/env node

/**
 * Debug WebSocket Connection
 * Simple test to see what's happening with WebSocket connections
 */

import { WebSocket } from 'ws';

const API_KEY = 'test_free_api_key_12345';
const WS_URL = `ws://localhost:3001/ws?apiKey=${API_KEY}`;

console.log('🧪 Debug WebSocket Connection for Free tier');
console.log('Expected: 1 connection limit');

async function testConnection(connectionNumber) {
    return new Promise((resolve) => {
        console.log(`\n🔌 Attempting connection ${connectionNumber}...`);
        
        const ws = new WebSocket(WS_URL);
        
        const timeout = setTimeout(() => {
            console.log(`⏰ Connection ${connectionNumber}: Timeout`);
            ws.terminate();
            resolve({ success: false, reason: 'timeout' });
        }, 3000);
        
        ws.on('open', () => {
            console.log(`✅ Connection ${connectionNumber}: Opened successfully`);
            clearTimeout(timeout);
            resolve({ success: true, ws });
        });
        
        ws.on('close', (code, reason) => {
            console.log(`❌ Connection ${connectionNumber}: Closed with code ${code}, reason: ${reason}`);
            clearTimeout(timeout);
            resolve({ success: false, code, reason: reason.toString() });
        });
        
        ws.on('error', (error) => {
            console.log(`❌ Connection ${connectionNumber}: Error - ${error.message}`);
            clearTimeout(timeout);
            resolve({ success: false, reason: error.message });
        });
    });
}

async function runTest() {
    const connections = [];
    
    // Try to open 3 connections sequentially
    for (let i = 1; i <= 3; i++) {
        const result = await testConnection(i);
        
        if (result.success) {
            connections.push(result.ws);
            console.log(`   Connection ${i} added to pool (total: ${connections.length})`);
        } else {
            console.log(`   Connection ${i} failed: ${result.reason || result.code}`);
            if (result.code === 1013) {
                console.log('   🎯 This looks like a connection limit rejection!');
                break;
            }
        }
        
        // Small delay between connections
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log(`\n📊 Final Results:`);
    console.log(`   Successful connections: ${connections.length}`);
    console.log(`   Expected limit: 1`);
    
    if (connections.length === 1) {
        console.log('   ✅ Connection limit appears to be working correctly!');
    } else if (connections.length > 1) {
        console.log('   ❌ Too many connections allowed - limit not working');
    } else {
        console.log('   ❌ No connections succeeded - something is wrong');
    }
    
    // Clean up
    console.log('\n🧹 Cleaning up connections...');
    connections.forEach((ws, index) => {
        if (ws.readyState === WebSocket.OPEN) {
            console.log(`   Closing connection ${index + 1}`);
            ws.close();
        }
    });
    
    setTimeout(() => {
        process.exit(0);
    }, 1000);
}

runTest().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
});
