{"info": {"_postman_id": "stalkapi-user-collection", "name": "StalkAPI - User Collection", "description": "Professional KOL (Key Opinion Leader) trading data API with real-time WebSocket streaming and historical data access.\n\n## Features\n- Real-time KOL trading stream via WebSocket (2 credits per message)\n- Historical KOL trading data via REST API (3 credits per request)\n- Credit-based usage tracking\n- Multi-tier access control\n- API key authentication\n\n## Authentication\n- API Key: Include in X-API-Key header\n- All endpoints require API key authentication\n- Registration/login are admin-only functions\n\n## Base URL\n- Development: `http://localhost:3001`\n- Production: `https://data.stalkapi.com`\n\n## Access Tiers\n- **Free**: 1,000 credits/month ($0.00) - 1 WebSocket connection\n- **Basic**: 1,000,000 credits/month ($49.99) - 3 WebSocket connections\n- **Premium**: 5,000,000 credits/month ($149.99) - 5 WebSocket connections\n- **Enterprise**: Unlimited credits ($499.99) - 10 WebSocket connections", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🏥 System Health", "item": [{"name": "API Root", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Get API information and available endpoints. No authentication required."}, "response": []}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check API health status. No authentication required."}, "response": []}, {"name": "API Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/status", "host": ["{{base_url}}"], "path": ["api", "v1", "status"]}, "description": "Get API status information. No authentication required."}, "response": []}], "description": "System health and status endpoints."}, {"name": "🔐 Authentication", "item": [{"name": "Get Usage Statistics", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/auth/usage?days=30", "host": ["{{base_url}}"], "path": ["auth", "usage"], "query": [{"key": "days", "value": "30", "description": "Number of days to get usage statistics for"}]}, "description": "Get user's API usage statistics for the specified period."}, "response": []}], "description": "User authentication and profile management."}, {"name": "📊 KOL Feed API", "item": [{"name": "KOL Feed History (API Key)", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/api/v1/kol-feed/history?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "kol-feed", "history"], "query": [{"key": "limit", "value": "10", "description": "Number of transactions to return (max 100)"}, {"key": "offset", "value": "0", "description": "Number of transactions to skip"}]}, "description": "Get historical KOL trading data using API Key authentication. Costs 3 credits per request."}, "response": []}], "description": "KOL trading activity data endpoints."}, {"name": "🔌 WebSocket API", "item": [{"name": "WebSocket Info", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/info", "host": ["{{base_url}}"], "path": ["ws-api", "info"]}, "description": "Get WebSocket server connection details and status."}, "response": []}, {"name": "Available Streams", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/streams", "host": ["{{base_url}}"], "path": ["ws-api", "streams"]}, "description": "Get list of available WebSocket streams for user's tier."}, "response": []}, {"name": "Active Sessions", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/sessions", "host": ["{{base_url}}"], "path": ["ws-api", "sessions"]}, "description": "Get information about user's active WebSocket sessions."}, "response": []}, {"name": "Usage Statistics", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/stats", "host": ["{{base_url}}"], "path": ["ws-api", "stats"]}, "description": "Get WebSocket usage statistics for the authenticated user."}, "response": []}, {"name": "Stream Credits Info", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/credits?stream=kol-feed", "host": ["{{base_url}}"], "path": ["ws-api", "credits"], "query": [{"key": "stream", "value": "kol-feed", "description": "Specific stream to get credit info for"}]}, "description": "Get credit costs and information for WebSocket streams."}, "response": []}, {"name": "Stream Credit Usage Stats", "request": {"method": "GET", "header": [{"key": "X-API-Key", "value": "{{api_key}}"}], "url": {"raw": "{{base_url}}/ws-api/credits/stats?days=7&stream=kol-feed", "host": ["{{base_url}}"], "path": ["ws-api", "credits", "stats"], "query": [{"key": "days", "value": "7", "description": "Number of days to get statistics for"}, {"key": "stream", "value": "kol-feed", "description": "Specific stream to get statistics for"}]}, "description": "Get detailed credit usage statistics for WebSocket streams."}, "response": []}], "description": "WebSocket server information and stream management endpoints."}], "variable": [{"key": "base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "api_key", "value": "2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG", "type": "string"}, {"key": "demo_email", "value": "<EMAIL>", "type": "string"}, {"key": "demo_password", "value": "demo123", "type": "string"}]}