import { pubsub, cache } from "../config/redis.js";
import { WebSocket } from "ws";

// Constants
const KOL_FEED_CHANNEL = "kol_feed_internal";
const KOL_FEED_HISTORY_KEY = "kol_feed_history";
const WS_SOURCES = "kol";
const WS_URL = `${process.env.STALKCHAIN_CENTRAL_WSS_URL}/feed?apiKey=${process.env.STALKCHAIN_CENTRAL_KEY}&sources=${WS_SOURCES}`;

function getTokenMint(token) {
  return token?.tokenAddress || token?.tokenAdress || null;
}

function transformKolFeedData(json) {
  if (!json?.tx) throw new Error("Missing tx in KOL feed message");
  const tx = json.tx;
  return {
    timestamp: json.timestamp,
    type: tx.transactionType,
    kol_label: tx.wallet_label,
    wallet: tx.isPublic ? tx.walletAddress : "private",
    kol_avatar: tx.wallet_avatar,
    token_in: {
      symbol: tx.tokenIn?.symbol,
      name: tx.tokenIn?.name,
      logo: tx.tokenIn?.logo,
      amount: tx.tokenIn?.amount,
      amount_string: tx.tokenIn?.tokenAmountString,
      amount_usd: tx.tokenIn?.tokenInAmountUsd,
      price: tx.tokenIn?.price,
      mint: getTokenMint(tx.tokenIn),
    },
    token_out: {
      symbol: tx.tokenOut?.symbol,
      name: tx.tokenOut?.name,
      logo: tx.tokenOut?.logo,
      amount: tx.tokenOut?.amount,
      amount_string: tx.tokenOut?.tokenAmountString,
      amount_usd: tx.tokenOut?.tokenOutAmountUsd,
      price: tx.tokenOut?.price,
      mint: getTokenMint(tx.tokenOut),
    },
    socials: tx.socials,
    signature: tx.isPublic ? tx.tx : "private",
  };
}

export class KolFeed {
  constructor() {
    this.isRunning = false;
    this.ws = null;
  }

  async init() {
    if (
      !process.env.STALKCHAIN_CENTRAL_WSS_URL ||
      !process.env.STALKCHAIN_CENTRAL_KEY
    ) {
      throw new Error(
        "Missing required environment variables for KolFeed WebSocket"
      );
    }
    this.isRunning = true;
    this.connect();
  }

  stop() {
    this.isRunning = false;
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  connect() {
    this.ws = new WebSocket(WS_URL);
    this.ws.on("open", () => {
      console.log("[KOLFeed] Connected to WebSocket");
    });

    this.ws.on("message", (message) => {
      try {
        const json = JSON.parse(message);
        if (process.env.NODE_ENV === "development") {
          // console.log("[KOLFeed] MESSAGE", JSON.stringify(json, null, 2));
        }
        if (json?.tx && json?.source === WS_SOURCES && json?.tx?.isKol) {
          const transformedData = transformKolFeedData(json);

          // Publish to pub/sub for real-time subscribers
          pubsub
            .publish(KOL_FEED_CHANNEL, transformedData)
            .then(() => {
              // console.log(`✅ [KOLFeed] Published message for ${json?.tx?.wallet_label || 'unknown'}`);
            })
            .catch((error) => {
              console.error("❌ [KOLFeed] Failed to publish message:", error);
            });

          // Store in Redis list for history (keep last 100 messages)
          cache
            .lpushAndTrim(KOL_FEED_HISTORY_KEY, transformedData, 100)
            .then((success) => {
              if (success) {
                console.log(
                  `✅ [KOLFeed] Stored message in history for ${
                    json?.tx?.wallet_label || "unknown"
                  }`
                );
              } else {
                console.error(
                  "❌ [KOLFeed] Failed to store message in history"
                );
              }
            })
            .catch((error) => {
              console.error(
                "❌ [KOLFeed] Error storing message in history:",
                error
              );
            });
        }
      } catch (error) {
        console.error("❌ [KOLFeed] Error processing message:", error);
      }
    });

    this.ws.on("error", (error) => {
      console.error("[KOLFeed] WebSocket error:", error);
      this.stop();
      // Optionally add exponential backoff here for reconnects
      if (this.isRunning) {
        setTimeout(() => this.connect(), 2000);
      }
    });

    this.ws.on("close", () => {
      console.log("[KOLFeed] WebSocket connection closed");
      if (this.isRunning) {
        setTimeout(() => this.connect(), 2000); // Add reconnect delay
      }
    });
  }
}

/* 
{
  type: 'txSwap',
  source: 'kol',
  tx: {
    tokenIn: {
      symbol: 'SOL',
      name: 'Wrapped SOL',
      logo: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png',
      tokenAmountString: '0.92',
      amount: 0.9197152919999994,
      tokenInAmountUsd: 146.465609,
      price: 159.251032,
      tokenAdress: 'So11111111111111111111111111111111111111112',
      tokenAddress: 'So11111111111111111111111111111111111111112'
    },
    tokenOut: {
      symbol: 'CAREBUBU',
      name: 'NEW CAREBEAR LABUBU DOLLS',
      logo: '',
      tokenAmountString: '16.71M',
      amount: 16708878.061636,
      tokenOutAmountUsd: 145.000953,
      price: 0.000009,
      tokenAdress: 'HRum1AZkwkG35nFBZavpHuadZHXVLss1bN8jp64Jpump',
      tokenAddress: 'HRum1AZkwkG35nFBZavpHuadZHXVLss1bN8jp64Jpump'
    },
    timestamp: 1748913012,
    chain: 'solana',
    tx: '5uTz1FZkUiZpe418qEUJ5a9w7bsnbhDNw7N4XouW9ufsTxBBQCE1FYqeipzjpcXqwWrH7Nji32hccBwso4NbErTr',
    walletAddress: '********************************************',
    wallet_label: 'Jidn',
    wallet_avatar: 'https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/jidn_w.jpg',
    isPublic: true,
    isKol: true,
    socials: [ [Object], [Object] ],
    totalUsd: 145,
    totalUsdNumber: 145.000953,
    transactionType: 'buy'
  },
  timestamp: 1748913955272
}
*/
