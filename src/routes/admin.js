import express from 'express';
import { randomUUID } from 'crypto';
import { query } from '../config/database.js';
import { cache } from '../config/redis.js';
import { User } from '../models/User.js';
import {
    requireAdmin,
    requirePermission,
    requireAnyPermission,
    logAdminAction,
    getAdminInfo
} from '../middleware/adminAuth.js';

const router = express.Router();

// Get all access tiers (including disabled ones)
router.get('/tiers', requirePermission('tiers:read'), logAdminAction('view_tiers'), async (req, res) => {
    try {
        const result = await query(
            `SELECT id, name, description, max_credits_per_month, max_requests_per_minute, 
                    max_websocket_connections, allowed_endpoints, allowed_streams, 
                    price_per_month, is_enabled, created_at, updated_at
             FROM access_tiers 
             ORDER BY id`
        );
        
        res.json({
            success: true,
            tiers: result.rows,
            admin: getAdminInfo(req.admin)
        });
        
    } catch (error) {
        console.error('Error fetching tiers:', error);
        res.status(500).json({
            error: 'Failed to fetch access tiers'
        });
    }
});

// Enable a tier
router.post('/tiers/:tierId/enable', requirePermission('tiers:write'), logAdminAction('enable_tier'), async (req, res) => {
    try {
        const { tierId } = req.params;
        
        const result = await query(
            'UPDATE access_tiers SET is_enabled = true WHERE id = $1 RETURNING *',
            [tierId]
        );
        
        if (result.rows.length === 0) {
            return res.status(404).json({
                error: 'Tier not found'
            });
        }
        
        // Clear relevant caches
        await clearTierCaches();
        
        res.json({
            success: true,
            message: `Tier '${result.rows[0].name}' has been enabled`,
            tier: result.rows[0]
        });
        
    } catch (error) {
        console.error('Error enabling tier:', error);
        res.status(500).json({
            error: 'Failed to enable tier'
        });
    }
});

// Disable a tier
router.post('/tiers/:tierId/disable', requirePermission('tiers:write'), logAdminAction('disable_tier'), async (req, res) => {
    try {
        const { tierId } = req.params;
        
        // Check if there are active users on this tier
        const userCount = await query(
            'SELECT COUNT(*) as count FROM users WHERE tier_id = $1 AND is_active = true',
            [tierId]
        );
        
        const activeUsers = parseInt(userCount.rows[0].count);
        
        if (activeUsers > 0) {
            return res.status(400).json({
                error: `Cannot disable tier: ${activeUsers} active users are currently using this tier`,
                active_users: activeUsers
            });
        }
        
        const result = await query(
            'UPDATE access_tiers SET is_enabled = false WHERE id = $1 RETURNING *',
            [tierId]
        );
        
        if (result.rows.length === 0) {
            return res.status(404).json({
                error: 'Tier not found'
            });
        }
        
        // Clear relevant caches
        await clearTierCaches();
        
        res.json({
            success: true,
            message: `Tier '${result.rows[0].name}' has been disabled`,
            tier: result.rows[0]
        });
        
    } catch (error) {
        console.error('Error disabling tier:', error);
        res.status(500).json({
            error: 'Failed to disable tier'
        });
    }
});

// Update tier configuration
router.put('/tiers/:tierId', requirePermission('tiers:write'), logAdminAction('update_tier'), async (req, res) => {
    try {
        const { tierId } = req.params;
        const {
            name,
            description,
            max_credits_per_month,
            max_requests_per_minute,
            max_websocket_connections,
            allowed_endpoints,
            allowed_streams,
            price_per_month
        } = req.body;
        
        const updates = [];
        const values = [];
        let paramCount = 1;
        
        if (name !== undefined) {
            updates.push(`name = $${paramCount}`);
            values.push(name);
            paramCount++;
        }
        
        if (description !== undefined) {
            updates.push(`description = $${paramCount}`);
            values.push(description);
            paramCount++;
        }
        
        if (max_credits_per_month !== undefined) {
            updates.push(`max_credits_per_month = $${paramCount}`);
            values.push(max_credits_per_month);
            paramCount++;
        }
        
        if (max_requests_per_minute !== undefined) {
            updates.push(`max_requests_per_minute = $${paramCount}`);
            values.push(max_requests_per_minute);
            paramCount++;
        }
        
        if (max_websocket_connections !== undefined) {
            updates.push(`max_websocket_connections = $${paramCount}`);
            values.push(max_websocket_connections);
            paramCount++;
        }
        
        if (allowed_endpoints !== undefined) {
            updates.push(`allowed_endpoints = $${paramCount}`);
            values.push(allowed_endpoints);
            paramCount++;
        }
        
        if (allowed_streams !== undefined) {
            updates.push(`allowed_streams = $${paramCount}`);
            values.push(allowed_streams);
            paramCount++;
        }
        
        if (price_per_month !== undefined) {
            updates.push(`price_per_month = $${paramCount}`);
            values.push(price_per_month);
            paramCount++;
        }
        
        if (updates.length === 0) {
            return res.status(400).json({
                error: 'No valid fields to update'
            });
        }
        
        updates.push(`updated_at = CURRENT_TIMESTAMP`);
        values.push(tierId);
        
        const result = await query(
            `UPDATE access_tiers SET ${updates.join(', ')} WHERE id = $${paramCount} RETURNING *`,
            values
        );
        
        if (result.rows.length === 0) {
            return res.status(404).json({
                error: 'Tier not found'
            });
        }
        
        // Clear relevant caches
        await clearTierCaches();
        
        res.json({
            success: true,
            message: 'Tier updated successfully',
            tier: result.rows[0]
        });
        
    } catch (error) {
        console.error('Error updating tier:', error);
        res.status(500).json({
            error: 'Failed to update tier'
        });
    }
});

// Get tier usage statistics
router.get('/tiers/:tierId/stats', requirePermission('tiers:read'), logAdminAction('view_tier_stats'), async (req, res) => {
    try {
        const { tierId } = req.params;
        
        const stats = await query(
            `SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN u.is_active = true THEN 1 END) as active_users,
                SUM(u.credits_remaining) as total_credits_remaining,
                SUM(u.credits_used_this_month) as total_credits_used_this_month,
                AVG(u.credits_remaining) as avg_credits_remaining
             FROM users u
             WHERE u.tier_id = $1`,
            [tierId]
        );
        
        const tierInfo = await query(
            'SELECT name, is_enabled FROM access_tiers WHERE id = $1',
            [tierId]
        );
        
        if (tierInfo.rows.length === 0) {
            return res.status(404).json({
                error: 'Tier not found'
            });
        }
        
        res.json({
            success: true,
            tier: tierInfo.rows[0],
            statistics: {
                total_users: parseInt(stats.rows[0].total_users),
                active_users: parseInt(stats.rows[0].active_users),
                total_credits_remaining: parseInt(stats.rows[0].total_credits_remaining) || 0,
                total_credits_used_this_month: parseInt(stats.rows[0].total_credits_used_this_month) || 0,
                avg_credits_remaining: parseFloat(stats.rows[0].avg_credits_remaining) || 0
            }
        });
        
    } catch (error) {
        console.error('Error fetching tier stats:', error);
        res.status(500).json({
            error: 'Failed to fetch tier statistics'
        });
    }
});

// Helper function to clear tier-related caches
async function clearTierCaches() {
    try {
        // Clear all user caches since tier information might have changed
        const keys = await cache.redis?.keys('user:*') || [];
        const apiKeys = await cache.redis?.keys('apikey:*') || [];
        
        if (keys.length > 0) {
            await cache.redis.del(...keys);
        }
        
        if (apiKeys.length > 0) {
            await cache.redis.del(...apiKeys);
        }
        
        console.log(`Cleared ${keys.length + apiKeys.length} tier-related cache entries`);
    } catch (error) {
        console.error('Error clearing tier caches:', error);
    }
}

// User management endpoints (for frontend backend integration)

// Register new user (admin only - for frontend backend)
router.post('/users/register', requirePermission('users:write'), logAdminAction('register_user'), async (req, res) => {
    try {
        const { email, password, tier_id } = req.body;

        // Validation
        if (!email || !password) {
            return res.status(400).json({
                error: 'Email and password are required'
            });
        }

        if (password.length < 8) {
            return res.status(400).json({
                error: 'Password must be at least 8 characters long'
            });
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                error: 'Invalid email format'
            });
        }

        // Create user
        const user = await User.create({
            email: email.toLowerCase(),
            password,
            tier_id: tier_id || 1 // Default to free tier
        });

        res.status(201).json({
            message: 'User created successfully',
            user: user.toJSON(),
            apiKey: user.api_key
        });

    } catch (error) {
        console.error('Registration error:', error);

        if (error.message === 'User already exists with this email') {
            return res.status(409).json({ error: error.message });
        }

        res.status(500).json({
            error: 'Registration failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Login user (admin only - for frontend backend)
router.post('/users/login', requirePermission('users:write'), logAdminAction('login_user'), async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                error: 'Email and password are required'
            });
        }

        // Authenticate user
        const user = await User.authenticate(email.toLowerCase(), password);

        if (!user) {
            return res.status(401).json({
                error: 'Invalid email or password'
            });
        }

        if (!user.is_active) {
            return res.status(401).json({
                error: 'Account is deactivated'
            });
        }

        res.json({
            message: 'Login successful',
            user: user.toJSON(),
            apiKey: user.api_key
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            error: 'Login failed',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// Admin user management endpoints

// Get current admin info
router.get('/me', requireAdmin(), async (req, res) => {
    try {
        res.json({
            success: true,
            admin: getAdminInfo(req.admin)
        });
    } catch (error) {
        console.error('Error getting admin info:', error);
        res.status(500).json({
            error: 'Failed to get admin information'
        });
    }
});

// Get all admin users (system admin only)
router.get('/admins', requirePermission('system:admin'), logAdminAction('view_admins'), async (req, res) => {
    try {
        const { Admin } = await import('../models/Admin.js');
        const admins = await Admin.getAll();

        res.json({
            success: true,
            admins: admins.map(admin => getAdminInfo(admin)),
            total: admins.length
        });

    } catch (error) {
        console.error('Error fetching admins:', error);
        res.status(500).json({
            error: 'Failed to fetch admin users'
        });
    }
});

// Create new admin user (system admin only)
router.post('/admins', requirePermission('system:admin'), logAdminAction('create_admin'), async (req, res) => {
    try {
        const { name, email, permissions = [] } = req.body;

        if (!name || !email) {
            return res.status(400).json({
                error: 'Name and email are required'
            });
        }

        const { Admin } = await import('../models/Admin.js');
        const apiKey = Admin.generateApiKey();

        const admin = await Admin.create({
            name,
            email,
            api_key: apiKey,
            permissions
        });

        if (!admin) {
            return res.status(400).json({
                error: 'Failed to create admin user',
                message: 'Email might already be in use'
            });
        }

        res.status(201).json({
            success: true,
            message: 'Admin user created successfully',
            admin: getAdminInfo(admin),
            api_key: apiKey // Only returned on creation
        });

    } catch (error) {
        console.error('Error creating admin:', error);
        res.status(500).json({
            error: 'Failed to create admin user'
        });
    }
});

// Update admin user (system admin only)
router.put('/admins/:adminId', requirePermission('system:admin'), logAdminAction('update_admin'), async (req, res) => {
    try {
        const { adminId } = req.params;
        const { name, email, permissions, is_active } = req.body;

        const updateData = {};
        if (name !== undefined) updateData.name = name;
        if (email !== undefined) updateData.email = email;
        if (permissions !== undefined) updateData.permissions = permissions;
        if (is_active !== undefined) updateData.is_active = is_active;

        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({
                error: 'No valid fields to update'
            });
        }

        const { Admin } = await import('../models/Admin.js');
        const admin = await Admin.update(adminId, updateData);

        if (!admin) {
            return res.status(404).json({
                error: 'Admin user not found'
            });
        }

        res.json({
            success: true,
            message: 'Admin user updated successfully',
            admin: getAdminInfo(admin)
        });

    } catch (error) {
        console.error('Error updating admin:', error);
        res.status(500).json({
            error: 'Failed to update admin user'
        });
    }
});

// Credit management endpoints

// Get user credit information
router.get('/users/:userId/credits', requirePermission('users:read'), logAdminAction('view_user_credits'), async (req, res) => {
    try {
        const { userId } = req.params;

        const result = await query(
            `SELECT
                u.id,
                u.email,
                u.credits_remaining,
                u.credits_used_this_month,
                u.total_credits_purchased,
                at.name as tier_name,
                at.max_credits_per_month,
                at.price_per_month
             FROM users u
             JOIN access_tiers at ON u.tier_id = at.id
             WHERE u.id = $1`,
            [userId]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({
                error: 'User not found'
            });
        }

        const user = result.rows[0];

        // Get recent credit usage
        const usageResult = await query(
            `SELECT
                endpoint,
                method,
                credits_consumed,
                created_at,
                ip_address
             FROM api_usage_logs
             WHERE user_id = $1
             ORDER BY created_at DESC
             LIMIT 20`,
            [userId]
        );

        res.json({
            success: true,
            user: {
                id: user.id,
                email: user.email,
                credits_remaining: user.credits_remaining,
                credits_used_this_month: user.credits_used_this_month,
                total_credits_purchased: user.total_credits_purchased,
                tier_name: user.tier_name,
                max_credits_per_month: user.max_credits_per_month,
                usage_percentage: user.max_credits_per_month > 0
                    ? (user.credits_used_this_month / user.max_credits_per_month * 100).toFixed(2)
                    : 0
            },
            recent_usage: usageResult.rows,
            admin: getAdminInfo(req.admin)
        });

    } catch (error) {
        console.error('Error getting user credits:', error);
        res.status(500).json({
            error: 'Failed to get user credit information'
        });
    }
});

// Add credits to user account
router.post('/users/:userId/credits', requirePermission('users:write'), logAdminAction('add_user_credits'), async (req, res) => {
    try {
        const { userId } = req.params;
        const { credits_to_add, reason = 'Admin adjustment' } = req.body;

        if (!credits_to_add || credits_to_add <= 0) {
            return res.status(400).json({
                error: 'credits_to_add must be a positive number'
            });
        }

        // Check if user exists
        const userCheck = await query(
            'SELECT email, credits_remaining FROM users WHERE id = $1',
            [userId]
        );

        if (userCheck.rows.length === 0) {
            return res.status(404).json({
                error: 'User not found'
            });
        }

        const oldCredits = userCheck.rows[0].credits_remaining;

        // Add credits to user
        const result = await query(
            `UPDATE users
             SET
                credits_remaining = credits_remaining + $1,
                total_credits_purchased = total_credits_purchased + $1,
                updated_at = CURRENT_TIMESTAMP
             WHERE id = $2
             RETURNING credits_remaining, total_credits_purchased`,
            [credits_to_add, userId]
        );

        // Log the credit addition (simplified logging)
        console.log(`✅ Admin ${req.admin.email} added ${credits_to_add} credits to user ${userResult.rows[0].email}`);

        res.json({
            success: true,
            message: `Added ${credits_to_add} credits to user account`,
            user: {
                email: userCheck.rows[0].email,
                credits_before: oldCredits,
                credits_after: result.rows[0].credits_remaining,
                credits_added: credits_to_add,
                total_credits_purchased: result.rows[0].total_credits_purchased
            },
            reason,
            admin: getAdminInfo(req.admin)
        });

    } catch (error) {
        console.error('Error adding user credits:', error);
        res.status(500).json({
            error: 'Failed to add credits to user account'
        });
    }
});

// Reset user monthly credits
router.post('/users/:userId/credits/reset', requirePermission('users:write'), logAdminAction('reset_user_credits'), async (req, res) => {
    try {
        const { userId } = req.params;

        // Get user and tier information
        const userResult = await query(
            `SELECT
                u.email,
                u.credits_used_this_month,
                at.max_credits_per_month
             FROM users u
             JOIN access_tiers at ON u.tier_id = at.id
             WHERE u.id = $1`,
            [userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({
                error: 'User not found'
            });
        }

        const user = userResult.rows[0];
        const creditsToRestore = user.max_credits_per_month > 0 ? user.max_credits_per_month : 0;

        // Reset monthly credits
        const result = await query(
            `UPDATE users
             SET
                credits_remaining = $1,
                credits_used_this_month = 0,
                updated_at = CURRENT_TIMESTAMP
             WHERE id = $2
             RETURNING credits_remaining`,
            [creditsToRestore, userId]
        );

        // Log the reset (simplified logging)
        console.log(`✅ Admin ${req.admin.email} reset credits for user ${userInfo.rows[0].email} to ${creditsToRestore}`);

        res.json({
            success: true,
            message: 'User monthly credits reset successfully',
            user: {
                email: user.email,
                credits_restored: creditsToRestore,
                credits_remaining: result.rows[0].credits_remaining,
                credits_used_this_month: 0
            },
            admin: getAdminInfo(req.admin)
        });

    } catch (error) {
        console.error('Error resetting user credits:', error);
        res.status(500).json({
            error: 'Failed to reset user monthly credits'
        });
    }
});

// Get credit usage analytics
router.get('/analytics/credits', requirePermission('analytics:read'), logAdminAction('view_credit_analytics'), async (req, res) => {
    try {
        const { period = 'month', limit = 10 } = req.query;

        // Top credit consumers
        const topConsumers = await query(
            `SELECT
                u.email,
                SUM(aul.credits_consumed) as total_credits_used,
                COUNT(*) as request_count,
                AVG(aul.credits_consumed) as avg_credits_per_request
             FROM api_usage_logs aul
             JOIN users u ON aul.user_id = u.id
             WHERE aul.created_at >= date_trunc($1, CURRENT_DATE)
             GROUP BY u.email
             ORDER BY total_credits_used DESC
             LIMIT $2`,
            [period, limit]
        );

        // Credit usage by endpoint
        const endpointUsage = await query(
            `SELECT
                endpoint,
                SUM(credits_consumed) as total_credits,
                COUNT(*) as request_count,
                AVG(credits_consumed) as avg_credits
             FROM api_usage_logs
             WHERE created_at >= date_trunc($1, CURRENT_DATE)
             GROUP BY endpoint
             ORDER BY total_credits DESC`,
            [period]
        );

        // Overall statistics
        const overallStats = await query(
            `SELECT
                SUM(credits_consumed) as total_credits_consumed,
                COUNT(*) as total_requests,
                COUNT(DISTINCT user_id) as unique_users,
                AVG(credits_consumed) as avg_credits_per_request
             FROM api_usage_logs
             WHERE created_at >= date_trunc($1, CURRENT_DATE)`,
            [period]
        );

        res.json({
            success: true,
            period,
            analytics: {
                overall: overallStats.rows[0],
                top_consumers: topConsumers.rows,
                endpoint_usage: endpointUsage.rows
            },
            admin: getAdminInfo(req.admin)
        });

    } catch (error) {
        console.error('Error getting credit analytics:', error);
        res.status(500).json({
            error: 'Failed to get credit analytics'
        });
    }
});

export default router;
